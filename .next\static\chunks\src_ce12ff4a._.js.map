{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,QAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,SAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,6LAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,6LAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,6LAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,6LAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,6LAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/shopping/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, ShoppingCartIcon, CheckIcon, XIcon, MapPinIcon, UsersIcon, DollarSignIcon, TrendingUpIcon, ShareIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { Modal } from '@/components/ui/Modal'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\nimport Link from 'next/link'\n\ntype ShoppingList = Database['public']['Tables']['shopping_lists']['Row']\ntype ShoppingListItem = Database['public']['Tables']['shopping_list_items']['Row']\ntype Store = Database['public']['Tables']['stores']['Row']\n\nexport default function ShoppingPage() {\n  const [shoppingLists, setShoppingLists] = useState<ShoppingList[]>([])\n  const [stores, setStores] = useState<Store[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [showStoreForm, setShowStoreForm] = useState(false)\n  const [newList, setNewList] = useState({\n    name: '',\n    description: '',\n    store_name: '',\n    store_address: '',\n    is_shared: false,\n  })\n  const [newStore, setNewStore] = useState({\n    name: '',\n    address: '',\n    store_type: 'grocery',\n  })\n  const [listStats, setListStats] = useState<{[key: string]: {itemCount: number, completedCount: number, totalCost: number}}>({})\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchShoppingLists()\n    fetchStores()\n\n    // Set up real-time subscriptions\n    const listsSubscription = supabase\n      .channel('shopping_lists_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'shopping_lists' },\n        (payload) => {\n          console.log('Shopping list change received:', payload)\n          fetchShoppingLists()\n        }\n      )\n      .subscribe()\n\n    const itemsSubscription = supabase\n      .channel('shopping_items_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'shopping_list_items' },\n        (payload) => {\n          console.log('Shopping item change received:', payload)\n          fetchShoppingLists() // Refresh to update stats\n        }\n      )\n      .subscribe()\n\n    return () => {\n      supabase.removeChannel(listsSubscription)\n      supabase.removeChannel(itemsSubscription)\n    }\n  }, [])\n\n  const fetchShoppingLists = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('shopping_lists')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setShoppingLists(data || [])\n\n      // Fetch stats for each list\n      if (data) {\n        await fetchListStats(data.map(list => list.id))\n      }\n    } catch (error) {\n      console.error('Error fetching shopping lists:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchStores = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('stores')\n        .select('*')\n        .order('name')\n\n      if (error) throw error\n      setStores(data || [])\n    } catch (error) {\n      console.error('Error fetching stores:', error)\n    }\n  }\n\n  const fetchListStats = async (listIds: string[]) => {\n    try {\n      const stats: {[key: string]: {itemCount: number, completedCount: number, totalCost: number}} = {}\n\n      for (const listId of listIds) {\n        const { data, error } = await supabase\n          .from('shopping_list_items')\n          .select('completed, actual_price, estimated_price, quantity')\n          .eq('shopping_list_id', listId)\n\n        if (error) throw error\n\n        const items = data || []\n        const itemCount = items.length\n        const completedCount = items.filter(item => item.completed).length\n        const totalCost = items.reduce((sum, item) => {\n          const price = item.actual_price || item.estimated_price || 0\n          return sum + (price * item.quantity)\n        }, 0)\n\n        stats[listId] = { itemCount, completedCount, totalCost }\n      }\n\n      setListStats(stats)\n    } catch (error) {\n      console.error('Error fetching list stats:', error)\n    }\n  }\n\n  const addShoppingList = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('shopping_lists').insert([\n        {\n          ...newList,\n          user_id: user.id,\n          store_name: newList.store_name || null,\n          store_address: newList.store_address || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewList({\n        name: '',\n        description: '',\n        store_name: '',\n        store_address: '',\n        is_shared: false,\n      })\n      setShowAddForm(false)\n      toast.success('Shopping list created successfully!')\n      fetchShoppingLists()\n    } catch (error) {\n      console.error('Error adding shopping list:', error)\n      toast.error('Failed to create shopping list. Please try again.')\n    }\n  }\n\n  const addStore = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { error } = await supabase.from('stores').insert([newStore])\n\n      if (error) throw error\n\n      setNewStore({\n        name: '',\n        address: '',\n        store_type: 'grocery',\n      })\n      setShowStoreForm(false)\n      toast.success('Store added successfully!')\n      fetchStores()\n    } catch (error) {\n      console.error('Error adding store:', error)\n      toast.error('Failed to add store. Please try again.')\n    }\n  }\n\n  const deleteShoppingList = async (listId: string) => {\n    if (!confirm('Are you sure you want to delete this shopping list?')) return\n\n    try {\n      const { error } = await supabase\n        .from('shopping_lists')\n        .delete()\n        .eq('id', listId)\n\n      if (error) throw error\n      toast.success('Shopping list deleted successfully!')\n      fetchShoppingLists()\n    } catch (error) {\n      console.error('Error deleting shopping list:', error)\n      toast.error('Failed to delete shopping list. Please try again.')\n    }\n  }\n\n  const shareShoppingList = async (listId: string, email: string) => {\n    try {\n      // First, find the user by email\n      const { data: profiles, error: profileError } = await supabase\n        .from('profiles')\n        .select('id')\n        .eq('email', email)\n        .single()\n\n      if (profileError || !profiles) {\n        toast.error('User not found with that email address.')\n        return\n      }\n\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      // Add collaborator\n      const { error } = await supabase.from('shopping_list_collaborators').insert([\n        {\n          shopping_list_id: listId,\n          user_id: profiles.id,\n          permission_level: 'edit',\n          invited_by: user.id,\n        },\n      ])\n\n      if (error) throw error\n\n      // Update list to mark as shared\n      await supabase\n        .from('shopping_lists')\n        .update({ is_shared: true })\n        .eq('id', listId)\n\n      toast.success('Shopping list shared successfully!')\n      fetchShoppingLists()\n    } catch (error) {\n      console.error('Error sharing shopping list:', error)\n      toast.error('Failed to share shopping list. Please try again.')\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Smart Shopping Lists</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Organize your shopping with price tracking, store optimization, and family sharing\n          </p>\n        </div>\n        <div className=\"flex flex-col sm:flex-row gap-3\">\n          <Button\n            onClick={() => setShowStoreForm(true)}\n            variant=\"outline\"\n          >\n            <MapPinIcon className=\"h-4 w-4 mr-2\" />\n            Add Store\n          </Button>\n          <Button\n            onClick={() => setShowAddForm(true)}\n            size=\"lg\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            New List\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n        <Card className=\"bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-blue-700\">Total Lists</p>\n                <p className=\"text-2xl font-bold text-blue-900\">\n                  {shoppingLists.length}\n                </p>\n              </div>\n              <div className=\"p-3 bg-blue-100 rounded-full\">\n                <ShoppingCartIcon className=\"h-6 w-6 text-blue-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gradient-to-br from-green-50 to-emerald-50 border-green-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-green-700\">Shared Lists</p>\n                <p className=\"text-2xl font-bold text-green-900\">\n                  {shoppingLists.filter(list => list.is_shared).length}\n                </p>\n              </div>\n              <div className=\"p-3 bg-green-100 rounded-full\">\n                <UsersIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-purple-700\">Total Estimated Cost</p>\n                <p className=\"text-2xl font-bold text-purple-900\">\n                  ${Object.values(listStats).reduce((sum, stats) => sum + stats.totalCost, 0).toFixed(2)}\n                </p>\n              </div>\n              <div className=\"p-3 bg-purple-100 rounded-full\">\n                <DollarSignIcon className=\"h-6 w-6 text-purple-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Add Shopping List Form */}\n      {showAddForm && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Create New Shopping List</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={addShoppingList}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    List Name\n                  </label>\n                  <Input\n                    id=\"name\"\n                    required\n                    value={newList.name}\n                    onChange={(e) => setNewList({ ...newList, name: e.target.value })}\n                    placeholder=\"e.g., Weekly Groceries, Party Supplies\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"store_name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Store Name (Optional)\n                  </label>\n                  <Input\n                    id=\"store_name\"\n                    value={newList.store_name}\n                    onChange={(e) => setNewList({ ...newList, store_name: e.target.value })}\n                    placeholder=\"e.g., Walmart, Target, Kroger\"\n                  />\n                </div>\n                <div className=\"md:col-span-2\">\n                  <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description (Optional)\n                  </label>\n                  <textarea\n                    id=\"description\"\n                    rows={3}\n                    className=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                    value={newList.description}\n                    onChange={(e) => setNewList({ ...newList, description: e.target.value })}\n                    placeholder=\"Add any notes about this shopping list...\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"store_address\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Store Address (Optional)\n                  </label>\n                  <Input\n                    id=\"store_address\"\n                    value={newList.store_address}\n                    onChange={(e) => setNewList({ ...newList, store_address: e.target.value })}\n                    placeholder=\"Store location for optimization\"\n                  />\n                </div>\n                <div className=\"flex items-center\">\n                  <input\n                    id=\"is_shared\"\n                    type=\"checkbox\"\n                    checked={newList.is_shared}\n                    onChange={(e) => setNewList({ ...newList, is_shared: e.target.checked })}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"is_shared\" className=\"ml-2 block text-sm text-gray-900\">\n                    Enable family sharing\n                  </label>\n                </div>\n              </div>\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setShowAddForm(false)}\n                >\n                  Cancel\n                </Button>\n                <Button type=\"submit\">\n                  Create List\n                </Button>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Add Store Form */}\n      {showStoreForm && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Add New Store</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={addStore}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"store_name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Store Name\n                  </label>\n                  <Input\n                    id=\"store_name\"\n                    required\n                    value={newStore.name}\n                    onChange={(e) => setNewStore({ ...newStore, name: e.target.value })}\n                    placeholder=\"e.g., Walmart Supercenter\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"store_type\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Store Type\n                  </label>\n                  <select\n                    id=\"store_type\"\n                    value={newStore.store_type}\n                    onChange={(e) => setNewStore({ ...newStore, store_type: e.target.value })}\n                    className=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  >\n                    <option value=\"grocery\">Grocery Store</option>\n                    <option value=\"pharmacy\">Pharmacy</option>\n                    <option value=\"department\">Department Store</option>\n                    <option value=\"warehouse\">Warehouse Club</option>\n                    <option value=\"convenience\">Convenience Store</option>\n                    <option value=\"specialty\">Specialty Store</option>\n                  </select>\n                </div>\n                <div className=\"md:col-span-2\">\n                  <label htmlFor=\"store_address\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Address\n                  </label>\n                  <Input\n                    id=\"store_address\"\n                    value={newStore.address}\n                    onChange={(e) => setNewStore({ ...newStore, address: e.target.value })}\n                    placeholder=\"Store address for location-based features\"\n                  />\n                </div>\n              </div>\n              <div className=\"mt-6 flex justify-end space-x-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setShowStoreForm(false)}\n                >\n                  Cancel\n                </Button>\n                <Button type=\"submit\">\n                  Add Store\n                </Button>\n              </div>\n            </form>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Shopping Lists Grid */}\n      {shoppingLists.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <ShoppingCartIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n          <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No shopping lists</h3>\n          <p className=\"mt-1 text-sm text-gray-500\">Get started by creating your first shopping list.</p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3\">\n          {shoppingLists.map((list) => {\n            const stats = listStats[list.id] || { itemCount: 0, completedCount: 0, totalCost: 0 }\n            const completionPercentage = stats.itemCount > 0 ? (stats.completedCount / stats.itemCount) * 100 : 0\n\n            return (\n              <motion.div\n                key={list.id}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                <Card className=\"h-full\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center flex-1\">\n                        <ShoppingCartIcon className=\"h-6 w-6 text-blue-600 flex-shrink-0\" />\n                        <div className=\"ml-3 flex-1 min-w-0\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {list.name}\n                          </h3>\n                          {list.store_name && (\n                            <div className=\"flex items-center mt-1\">\n                              <MapPinIcon className=\"h-4 w-4 text-gray-400 mr-1\" />\n                              <p className=\"text-sm text-gray-600 truncate\">{list.store_name}</p>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2 ml-2\">\n                        {list.is_shared && (\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            <UsersIcon className=\"h-3 w-3 mr-1\" />\n                            Shared\n                          </Badge>\n                        )}\n                        <button\n                          onClick={() => deleteShoppingList(list.id)}\n                          className=\"text-red-600 hover:text-red-900 p-1\"\n                        >\n                          <XIcon className=\"h-4 w-4\" />\n                        </button>\n                      </div>\n                    </div>\n\n                    {list.description && (\n                      <p className=\"mt-3 text-sm text-gray-600 line-clamp-2\">{list.description}</p>\n                    )}\n\n                    {/* Progress and Stats */}\n                    <div className=\"mt-4 space-y-3\">\n                      <div className=\"flex justify-between items-center text-sm\">\n                        <span className=\"text-gray-600\">Progress</span>\n                        <span className=\"font-medium\">\n                          {stats.completedCount}/{stats.itemCount} items\n                        </span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div\n                          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                          style={{ width: `${completionPercentage}%` }}\n                        />\n                      </div>\n\n                      {stats.totalCost > 0 && (\n                        <div className=\"flex items-center justify-between text-sm\">\n                          <span className=\"text-gray-600\">Estimated Cost</span>\n                          <span className=\"font-medium text-green-600\">\n                            ${stats.totalCost.toFixed(2)}\n                          </span>\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Actions */}\n                    <div className=\"mt-6 flex space-x-2\">\n                      <Link href={`/shopping/${list.id}`} className=\"flex-1\">\n                        <Button variant=\"outline\" className=\"w-full\">\n                          View Items\n                        </Button>\n                      </Link>\n                      {!list.is_shared && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => {\n                            const email = prompt('Enter email address to share with:')\n                            if (email) shareShoppingList(list.id, email)\n                          }}\n                        >\n                          <ShareIcon className=\"h-4 w-4\" />\n                        </Button>\n                      )}\n                    </div>\n\n                    <div className=\"mt-4 pt-4 border-t border-gray-200\">\n                      <p className=\"text-xs text-gray-500\">\n                        Created {new Date(list.created_at).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            )\n          })}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;;;AAdA;;;;;;;;;;;AAoBe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,MAAM;QACN,aAAa;QACb,YAAY;QACZ,eAAe;QACf,WAAW;IACb;IACA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,YAAY;IACd;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmF,CAAC;IAC7H,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;YACA;YAEA,iCAAiC;YACjC,MAAM,oBAAoB,SACvB,OAAO,CAAC,0BACR,EAAE,CAAC,oBACF;gBAAE,OAAO;gBAAK,QAAQ;gBAAU,OAAO;YAAiB;4DACxD,CAAC;oBACC,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C;gBACF;2DAED,SAAS;YAEZ,MAAM,oBAAoB,SACvB,OAAO,CAAC,0BACR,EAAE,CAAC,oBACF;gBAAE,OAAO;gBAAK,QAAQ;gBAAU,OAAO;YAAsB;4DAC7D,CAAC;oBACC,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,qBAAqB,0BAA0B;;gBACjD;2DAED,SAAS;YAEZ;0CAAO;oBACL,SAAS,aAAa,CAAC;oBACvB,SAAS,aAAa,CAAC;gBACzB;;QACF;iCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;YAE3B,4BAA4B;YAC5B,IAAI,MAAM;gBACR,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,UACL,MAAM,CAAC,KACP,KAAK,CAAC;YAET,IAAI,OAAO,MAAM;YACjB,UAAU,QAAQ,EAAE;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAyF,CAAC;YAEhG,KAAK,MAAM,UAAU,QAAS;gBAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,uBACL,MAAM,CAAC,sDACP,EAAE,CAAC,oBAAoB;gBAE1B,IAAI,OAAO,MAAM;gBAEjB,MAAM,QAAQ,QAAQ,EAAE;gBACxB,MAAM,YAAY,MAAM,MAAM;gBAC9B,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;gBAClE,MAAM,YAAY,MAAM,MAAM,CAAC,CAAC,KAAK;oBACnC,MAAM,QAAQ,KAAK,YAAY,IAAI,KAAK,eAAe,IAAI;oBAC3D,OAAO,MAAO,QAAQ,KAAK,QAAQ;gBACrC,GAAG;gBAEH,KAAK,CAAC,OAAO,GAAG;oBAAE;oBAAW;oBAAgB;gBAAU;YACzD;YAEA,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,MAAM,CAAC;gBAC7D;oBACE,GAAG,OAAO;oBACV,SAAS,KAAK,EAAE;oBAChB,YAAY,QAAQ,UAAU,IAAI;oBAClC,eAAe,QAAQ,aAAa,IAAI;gBAC1C;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,WAAW;gBACT,MAAM;gBACN,aAAa;gBACb,YAAY;gBACZ,eAAe;gBACf,WAAW;YACb;YACA,eAAe;YACf,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,MAAM,CAAC;gBAAC;aAAS;YAEjE,IAAI,OAAO,MAAM;YAEjB,YAAY;gBACV,MAAM;gBACN,SAAS;gBACT,YAAY;YACd;YACA,iBAAiB;YACjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,wDAAwD;QAErE,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YACjB,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,OAAO,QAAgB;QAC/C,IAAI;YACF,gCAAgC;YAChC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,SAAS,OACZ,MAAM;YAET,IAAI,gBAAgB,CAAC,UAAU;gBAC7B,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,mBAAmB;YACnB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,+BAA+B,MAAM,CAAC;gBAC1E;oBACE,kBAAkB;oBAClB,SAAS,SAAS,EAAE;oBACpB,kBAAkB;oBAClB,YAAY,KAAK,EAAE;gBACrB;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,gCAAgC;YAChC,MAAM,SACH,IAAI,CAAC,kBACL,MAAM,CAAC;gBAAE,WAAW;YAAK,GACzB,EAAE,CAAC,MAAM;YAEZ,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,iBAAiB;gCAChC,SAAQ;;kDAER,6LAAC,iNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,eAAe;gCAC9B,MAAK;;kDAEL,6LAAC,yMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DACV,cAAc,MAAM;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,6NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpC,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DACV,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;;;;;;;;;;;;kDAGxD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;;oDAAqC;oDAC9C,OAAO,MAAM,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,SAAS,EAAE,GAAG,OAAO,CAAC;;;;;;;;;;;;;kDAGxF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQnC,6BACC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAO,WAAU;8DAA+C;;;;;;8DAG/E,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,QAAQ;oDACR,OAAO,QAAQ,IAAI;oDACnB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC/D,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAA+C;;;;;;8DAGrF,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,UAAU;oDACzB,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACrE,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA+C;;;;;;8DAGtF,6LAAC;oDACC,IAAG;oDACH,MAAM;oDACN,WAAU;oDACV,OAAO,QAAQ,WAAW;oDAC1B,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtE,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA+C;;;;;;8DAGxF,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,QAAQ,aAAa;oDAC5B,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACxE,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,SAAS,QAAQ,SAAS;oDAC1B,UAAU,CAAC,IAAM,WAAW;4DAAE,GAAG,OAAO;4DAAE,WAAW,EAAE,MAAM,CAAC,OAAO;wDAAC;oDACtE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;8CAK5E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,eAAe;sDAC/B;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU/B,+BACC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;;8CACd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAA+C;;;;;;8DAGrF,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,QAAQ;oDACR,OAAO,SAAS,IAAI;oDACpB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjE,aAAY;;;;;;;;;;;;sDAGhB,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAA+C;;;;;;8DAGrF,6LAAC;oDACC,IAAG;oDACH,OAAO,SAAS,UAAU;oDAC1B,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACvE,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,6LAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,6LAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,6LAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,6LAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,6LAAC;4DAAO,OAAM;sEAAY;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA+C;;;;;;8DAGxF,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,OAAO,SAAS,OAAO;oDACvB,UAAU,CAAC,IAAM,YAAY;4DAAE,GAAG,QAAQ;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS,IAAM,iBAAiB;sDACjC;;;;;;sDAGD,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAU/B,cAAc,MAAM,KAAK,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6NAAA,CAAA,mBAAgB;wBAAC,WAAU;;;;;;kCAC5B,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;qCAG5C,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC;oBAClB,MAAM,QAAQ,SAAS,CAAC,KAAK,EAAE,CAAC,IAAI;wBAAE,WAAW;wBAAG,gBAAgB;wBAAG,WAAW;oBAAE;oBACpF,MAAM,uBAAuB,MAAM,SAAS,GAAG,IAAI,AAAC,MAAM,cAAc,GAAG,MAAM,SAAS,GAAI,MAAM;oBAEpG,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6NAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;kEAC5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,KAAK,IAAI;;;;;;4DAEX,KAAK,UAAU,kBACd,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,iNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;kFACtB,6LAAC;wEAAE,WAAU;kFAAkC,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;0DAKtE,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,SAAS,kBACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;0EACnC,6LAAC,2MAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAI1C,6LAAC;wDACC,SAAS,IAAM,mBAAmB,KAAK,EAAE;wDACzC,WAAU;kEAEV,cAAA,6LAAC,mMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;oCAKtB,KAAK,WAAW,kBACf,6LAAC;wCAAE,WAAU;kDAA2C,KAAK,WAAW;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DACb,MAAM,cAAc;4DAAC;4DAAE,MAAM,SAAS;4DAAC;;;;;;;;;;;;;0DAG5C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,qBAAqB,CAAC,CAAC;oDAAC;;;;;;;;;;;4CAI9C,MAAM,SAAS,GAAG,mBACjB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;;4DAA6B;4DACzC,MAAM,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;kDAOlC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE;gDAAE,WAAU;0DAC5C,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,WAAU;8DAAS;;;;;;;;;;;4CAI9C,CAAC,KAAK,SAAS,kBACd,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;oDACP,MAAM,QAAQ,OAAO;oDACrB,IAAI,OAAO,kBAAkB,KAAK,EAAE,EAAE;gDACxC;0DAEA,cAAA,6LAAC,2MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK3B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC1B,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;uBA1FxD,KAAK,EAAE;;;;;gBAiGlB;;;;;;;;;;;;AAKV;GA/kBwB;KAAA", "debugId": null}}]}