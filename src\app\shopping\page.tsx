'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { PlusIcon, ShoppingCartIcon, CheckIcon, XIcon, MapPinIcon, UsersIcon, DollarSignIcon, TrendingUpIcon, ShareIcon } from 'lucide-react'
import { Database } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Input } from '@/components/ui/Input'
import { Loading } from '@/components/ui/Loading'
import { Modal } from '@/components/ui/Modal'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import Link from 'next/link'

type ShoppingList = Database['public']['Tables']['shopping_lists']['Row']
type ShoppingListItem = Database['public']['Tables']['shopping_list_items']['Row']
type Store = Database['public']['Tables']['stores']['Row']

export default function ShoppingPage() {
  const [shoppingLists, setShoppingLists] = useState<ShoppingList[]>([])
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showStoreForm, setShowStoreForm] = useState(false)
  const [newList, setNewList] = useState({
    name: '',
    description: '',
    store_name: '',
    store_address: '',
    is_shared: false,
  })
  const [newStore, setNewStore] = useState({
    name: '',
    address: '',
    store_type: 'grocery',
  })
  const [listStats, setListStats] = useState<{[key: string]: {itemCount: number, completedCount: number, totalCost: number}}>({})
  const supabase = createClient()

  useEffect(() => {
    fetchShoppingLists()
    fetchStores()

    // Set up real-time subscriptions
    const listsSubscription = supabase
      .channel('shopping_lists_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'shopping_lists' },
        (payload) => {
          console.log('Shopping list change received:', payload)
          fetchShoppingLists()
        }
      )
      .subscribe()

    const itemsSubscription = supabase
      .channel('shopping_items_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'shopping_list_items' },
        (payload) => {
          console.log('Shopping item change received:', payload)
          fetchShoppingLists() // Refresh to update stats
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(listsSubscription)
      supabase.removeChannel(itemsSubscription)
    }
  }, [])

  const fetchShoppingLists = async () => {
    try {
      const { data, error } = await supabase
        .from('shopping_lists')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setShoppingLists(data || [])

      // Fetch stats for each list
      if (data) {
        await fetchListStats(data.map(list => list.id))
      }
    } catch (error) {
      console.error('Error fetching shopping lists:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStores = async () => {
    try {
      const { data, error } = await supabase
        .from('stores')
        .select('*')
        .order('name')

      if (error) throw error
      setStores(data || [])
    } catch (error) {
      console.error('Error fetching stores:', error)
    }
  }

  const fetchListStats = async (listIds: string[]) => {
    try {
      const stats: {[key: string]: {itemCount: number, completedCount: number, totalCost: number}} = {}

      for (const listId of listIds) {
        const { data, error } = await supabase
          .from('shopping_list_items')
          .select('completed, actual_price, estimated_price, quantity')
          .eq('shopping_list_id', listId)

        if (error) throw error

        const items = data || []
        const itemCount = items.length
        const completedCount = items.filter(item => item.completed).length
        const totalCost = items.reduce((sum, item) => {
          const price = item.actual_price || item.estimated_price || 0
          return sum + (price * item.quantity)
        }, 0)

        stats[listId] = { itemCount, completedCount, totalCost }
      }

      setListStats(stats)
    } catch (error) {
      console.error('Error fetching list stats:', error)
    }
  }

  const addShoppingList = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { error } = await supabase.from('shopping_lists').insert([
        {
          ...newList,
          user_id: user.id,
          store_name: newList.store_name || null,
          store_address: newList.store_address || null,
        },
      ])

      if (error) throw error

      setNewList({
        name: '',
        description: '',
        store_name: '',
        store_address: '',
        is_shared: false,
      })
      setShowAddForm(false)
      toast.success('Shopping list created successfully!')
      fetchShoppingLists()
    } catch (error) {
      console.error('Error adding shopping list:', error)
      toast.error('Failed to create shopping list. Please try again.')
    }
  }

  const addStore = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { error } = await supabase.from('stores').insert([newStore])

      if (error) throw error

      setNewStore({
        name: '',
        address: '',
        store_type: 'grocery',
      })
      setShowStoreForm(false)
      toast.success('Store added successfully!')
      fetchStores()
    } catch (error) {
      console.error('Error adding store:', error)
      toast.error('Failed to add store. Please try again.')
    }
  }

  const deleteShoppingList = async (listId: string) => {
    if (!confirm('Are you sure you want to delete this shopping list?')) return

    try {
      const { error } = await supabase
        .from('shopping_lists')
        .delete()
        .eq('id', listId)

      if (error) throw error
      toast.success('Shopping list deleted successfully!')
      fetchShoppingLists()
    } catch (error) {
      console.error('Error deleting shopping list:', error)
      toast.error('Failed to delete shopping list. Please try again.')
    }
  }

  const shareShoppingList = async (listId: string, email: string) => {
    try {
      // First, find the user by email
      const { data: profiles, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('email', email)
        .single()

      if (profileError || !profiles) {
        toast.error('User not found with that email address.')
        return
      }

      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      // Add collaborator
      const { error } = await supabase.from('shopping_list_collaborators').insert([
        {
          shopping_list_id: listId,
          user_id: profiles.id,
          permission_level: 'edit',
          invited_by: user.id,
        },
      ])

      if (error) throw error

      // Update list to mark as shared
      await supabase
        .from('shopping_lists')
        .update({ is_shared: true })
        .eq('id', listId)

      toast.success('Shopping list shared successfully!')
      fetchShoppingLists()
    } catch (error) {
      console.error('Error sharing shopping list:', error)
      toast.error('Failed to share shopping list. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Smart Shopping Lists</h1>
          <p className="mt-2 text-gray-600">
            Organize your shopping with price tracking, store optimization, and family sharing
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={() => setShowStoreForm(true)}
            variant="outline"
          >
            <MapPinIcon className="h-4 w-4 mr-2" />
            Add Store
          </Button>
          <Button
            onClick={() => setShowAddForm(true)}
            size="lg"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New List
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700">Total Lists</p>
                <p className="text-2xl font-bold text-blue-900">
                  {shoppingLists.length}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <ShoppingCartIcon className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">Shared Lists</p>
                <p className="text-2xl font-bold text-green-900">
                  {shoppingLists.filter(list => list.is_shared).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <UsersIcon className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700">Total Estimated Cost</p>
                <p className="text-2xl font-bold text-purple-900">
                  ${Object.values(listStats).reduce((sum, stats) => sum + stats.totalCost, 0).toFixed(2)}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <DollarSignIcon className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Shopping List Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Shopping List</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={addShoppingList}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    List Name
                  </label>
                  <Input
                    id="name"
                    required
                    value={newList.name}
                    onChange={(e) => setNewList({ ...newList, name: e.target.value })}
                    placeholder="e.g., Weekly Groceries, Party Supplies"
                  />
                </div>
                <div>
                  <label htmlFor="store_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Store Name (Optional)
                  </label>
                  <Input
                    id="store_name"
                    value={newList.store_name}
                    onChange={(e) => setNewList({ ...newList, store_name: e.target.value })}
                    placeholder="e.g., Walmart, Target, Kroger"
                  />
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    id="description"
                    rows={3}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    value={newList.description}
                    onChange={(e) => setNewList({ ...newList, description: e.target.value })}
                    placeholder="Add any notes about this shopping list..."
                  />
                </div>
                <div>
                  <label htmlFor="store_address" className="block text-sm font-medium text-gray-700 mb-1">
                    Store Address (Optional)
                  </label>
                  <Input
                    id="store_address"
                    value={newList.store_address}
                    onChange={(e) => setNewList({ ...newList, store_address: e.target.value })}
                    placeholder="Store location for optimization"
                  />
                </div>
                <div className="flex items-center">
                  <input
                    id="is_shared"
                    type="checkbox"
                    checked={newList.is_shared}
                    onChange={(e) => setNewList({ ...newList, is_shared: e.target.checked })}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_shared" className="ml-2 block text-sm text-gray-900">
                    Enable family sharing
                  </label>
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  Create List
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Add Store Form */}
      {showStoreForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Store</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={addStore}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="store_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Store Name
                  </label>
                  <Input
                    id="store_name"
                    required
                    value={newStore.name}
                    onChange={(e) => setNewStore({ ...newStore, name: e.target.value })}
                    placeholder="e.g., Walmart Supercenter"
                  />
                </div>
                <div>
                  <label htmlFor="store_type" className="block text-sm font-medium text-gray-700 mb-1">
                    Store Type
                  </label>
                  <select
                    id="store_type"
                    value={newStore.store_type}
                    onChange={(e) => setNewStore({ ...newStore, store_type: e.target.value })}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="grocery">Grocery Store</option>
                    <option value="pharmacy">Pharmacy</option>
                    <option value="department">Department Store</option>
                    <option value="warehouse">Warehouse Club</option>
                    <option value="convenience">Convenience Store</option>
                    <option value="specialty">Specialty Store</option>
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label htmlFor="store_address" className="block text-sm font-medium text-gray-700 mb-1">
                    Address
                  </label>
                  <Input
                    id="store_address"
                    value={newStore.address}
                    onChange={(e) => setNewStore({ ...newStore, address: e.target.value })}
                    placeholder="Store address for location-based features"
                  />
                </div>
              </div>
              <div className="mt-6 flex justify-end space-x-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowStoreForm(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  Add Store
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Shopping Lists Grid */}
      {shoppingLists.length === 0 ? (
        <div className="text-center py-12">
          <ShoppingCartIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No shopping lists</h3>
          <p className="mt-1 text-sm text-gray-500">Get started by creating your first shopping list.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {shoppingLists.map((list) => {
            const stats = listStats[list.id] || { itemCount: 0, completedCount: 0, totalCost: 0 }
            const completionPercentage = stats.itemCount > 0 ? (stats.completedCount / stats.itemCount) * 100 : 0

            return (
              <motion.div
                key={list.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="h-full">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center flex-1">
                        <ShoppingCartIcon className="h-6 w-6 text-blue-600 flex-shrink-0" />
                        <div className="ml-3 flex-1 min-w-0">
                          <h3 className="text-lg font-medium text-gray-900 truncate">
                            {list.name}
                          </h3>
                          {list.store_name && (
                            <div className="flex items-center mt-1">
                              <MapPinIcon className="h-4 w-4 text-gray-400 mr-1" />
                              <p className="text-sm text-gray-600 truncate">{list.store_name}</p>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-2">
                        {list.is_shared && (
                          <Badge variant="secondary" className="text-xs">
                            <UsersIcon className="h-3 w-3 mr-1" />
                            Shared
                          </Badge>
                        )}
                        <button
                          onClick={() => deleteShoppingList(list.id)}
                          className="text-red-600 hover:text-red-900 p-1"
                        >
                          <XIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {list.description && (
                      <p className="mt-3 text-sm text-gray-600 line-clamp-2">{list.description}</p>
                    )}

                    {/* Progress and Stats */}
                    <div className="mt-4 space-y-3">
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">Progress</span>
                        <span className="font-medium">
                          {stats.completedCount}/{stats.itemCount} items
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${completionPercentage}%` }}
                        />
                      </div>

                      {stats.totalCost > 0 && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Estimated Cost</span>
                          <span className="font-medium text-green-600">
                            ${stats.totalCost.toFixed(2)}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="mt-6 flex space-x-2">
                      <Link href={`/shopping/${list.id}`} className="flex-1">
                        <Button variant="outline" className="w-full">
                          View Items
                        </Button>
                      </Link>
                      {!list.is_shared && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const email = prompt('Enter email address to share with:')
                            if (email) shareShoppingList(list.id, email)
                          }}
                        >
                          <ShareIcon className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        Created {new Date(list.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>
      )}
    </div>
  )
}
