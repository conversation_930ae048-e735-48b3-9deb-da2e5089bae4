-- Enhance shopping_lists table
ALTER TABLE public.shopping_lists 
ADD COLUMN store_name TEXT,
ADD COLUMN store_address TEXT,
ADD COLUMN is_shared BOOLEAN DEFAULT FALSE,
ADD COLUMN shared_with TEXT[] DEFAULT '{}',
ADD COLUMN total_estimated_cost DECIMAL(10,2) DEFAULT 0,
ADD COLUMN total_actual_cost DECIMAL(10,2) DEFAULT 0;

-- Enhance shopping_list_items table  
ALTER TABLE public.shopping_list_items
ADD COLUMN estimated_price DECIMAL(10,2),
ADD COLUMN actual_price DECIMAL(10,2),
ADD COLUMN category TEXT,
ADD COLUMN priority INTEGER DEFAULT 1,
ADD COLUMN recipe_id UUID REFERENCES public.recipes(id) ON DELETE SET NULL,
ADD COLUMN store_section TEXT,
ADD COLUMN brand_preference TEXT,
ADD COLUMN is_organic BOOLEAN DEFAULT FALSE,
ADD COLUMN barcode TEXT;

-- Create stores table for store optimization
CREATE TABLE public.stores (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  website TEXT,
  store_type TEXT, -- grocery, pharmacy, department, etc.
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create store_sections table for aisle optimization
CREATE TABLE public.store_sections (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  store_id UUID REFERENCES public.stores(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  section_order INTEGER DEFAULT 1,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create price_history table for price tracking
CREATE TABLE public.price_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  item_name TEXT NOT NULL,
  store_id UUID REFERENCES public.stores(id) ON DELETE CASCADE,
  price DECIMAL(10,2) NOT NULL,
  unit TEXT,
  brand TEXT,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL
);

-- Create shopping_list_collaborators table for family sharing
CREATE TABLE public.shopping_list_collaborators (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  shopping_list_id UUID REFERENCES public.shopping_lists(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  permission_level TEXT CHECK (permission_level IN ('view', 'edit', 'admin')) DEFAULT 'edit',
  invited_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  UNIQUE(shopping_list_id, user_id)
);

-- Add indexes for better performance
CREATE INDEX idx_shopping_list_items_recipe_id ON public.shopping_list_items(recipe_id);
CREATE INDEX idx_shopping_list_items_category ON public.shopping_list_items(category);
CREATE INDEX idx_stores_location ON public.stores(latitude, longitude);
CREATE INDEX idx_price_history_item_store ON public.price_history(item_name, store_id);
CREATE INDEX idx_shopping_list_collaborators_list_id ON public.shopping_list_collaborators(shopping_list_id);
CREATE INDEX idx_shopping_list_collaborators_user_id ON public.shopping_list_collaborators(user_id);

-- Add comments
COMMENT ON COLUMN public.shopping_lists.store_name IS 'Primary store for this shopping list';
COMMENT ON COLUMN public.shopping_lists.is_shared IS 'Whether this list is shared with other users';
COMMENT ON COLUMN public.shopping_lists.shared_with IS 'Array of user emails shared with (deprecated - use collaborators table)';
COMMENT ON COLUMN public.shopping_list_items.estimated_price IS 'Estimated price per unit';
COMMENT ON COLUMN public.shopping_list_items.actual_price IS 'Actual price paid per unit';
COMMENT ON COLUMN public.shopping_list_items.priority IS 'Priority level (1=high, 2=medium, 3=low)';
COMMENT ON COLUMN public.shopping_list_items.recipe_id IS 'Recipe this item is needed for';
COMMENT ON COLUMN public.shopping_list_items.store_section IS 'Store aisle/section where item is located';
