{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "f05fa06083dd5c6080fea80010299f2b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8ad6511cc8ab017a763d022d5b041d146af80a4abcf8e8b6ba7f4f916693f32c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a9468f7c0a11330f537fa15cc0efc391e2ce2435d3b585c4227b9dccb8692aa0"}}}, "sortedMiddleware": ["/"], "functions": {}}