{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Lzxq+LF28sVcjYZHZyFkzVYZUvwisl0bINLEPMHycW8=", "__NEXT_PREVIEW_MODE_ID": "6654c7fcebfc6e5ccdfd8a3265a8cf55", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "af1d4b018fea72fe90dd271126fdcde1192c050e4a4091833aca56806ab5d62e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2496b928c79bf22bac0442c4c7f6ff6f91ae79681b3b0ff556e844f15f0f442a"}}}, "sortedMiddleware": ["/"], "functions": {}}