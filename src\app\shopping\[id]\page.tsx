'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useParams, useRouter } from 'next/navigation'
import { PlusIcon, CheckIcon, XIcon, ArrowLeftIcon, DollarSignIcon, MapPinIcon, TagIcon, AlertCircleIcon, TrendingUpIcon, UsersIcon, ShoppingCartIcon } from 'lucide-react'
import { Database } from '@/lib/types/database'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Input } from '@/components/ui/Input'
import { Loading } from '@/components/ui/Loading'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import Link from 'next/link'

type ShoppingList = Database['public']['Tables']['shopping_lists']['Row']
type ShoppingListItem = Database['public']['Tables']['shopping_list_items']['Row']
type Recipe = Database['public']['Tables']['recipes']['Row']

export default function ShoppingListDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [shoppingList, setShoppingList] = useState<ShoppingList | null>(null)
  const [items, setItems] = useState<ShoppingListItem[]>([])
  const [recipes, setRecipes] = useState<Recipe[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showBulkAdd, setShowBulkAdd] = useState(false)
  const [newItem, setNewItem] = useState({
    name: '',
    quantity: 1,
    unit: '',
    notes: '',
    estimated_price: '',
    category: '',
    priority: 1,
    recipe_id: '',
    store_section: '',
    brand_preference: '',
    is_organic: false,
  })
  const [bulkItems, setBulkItems] = useState('')
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'priority' | 'price'>('priority')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const supabase = createClient()

  useEffect(() => {
    if (params.id) {
      fetchShoppingListData()
      fetchRecipes()
    }

    // Set up real-time subscriptions
    const itemsSubscription = supabase
      .channel('shopping_items_detail_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'shopping_list_items', filter: `shopping_list_id=eq.${params.id}` },
        (payload) => {
          console.log('Shopping item change received:', payload)
          fetchShoppingListData()
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(itemsSubscription)
    }
  }, [params.id])

  const fetchShoppingListData = async () => {
    try {
      const [listResult, itemsResult] = await Promise.all([
        supabase
          .from('shopping_lists')
          .select('*')
          .eq('id', params.id)
          .single(),
        supabase
          .from('shopping_list_items')
          .select('*, recipes(title)')
          .eq('shopping_list_id', params.id)
          .order('priority', { ascending: true })
          .order('created_at', { ascending: false }),
      ])

      if (listResult.error) throw listResult.error
      if (itemsResult.error) throw itemsResult.error

      setShoppingList(listResult.data)
      setItems(itemsResult.data || [])
    } catch (error) {
      console.error('Error fetching shopping list data:', error)
      router.push('/shopping')
    } finally {
      setLoading(false)
    }
  }

  const fetchRecipes = async () => {
    try {
      const { data, error } = await supabase
        .from('recipes')
        .select('id, title')
        .order('title')

      if (error) throw error
      setRecipes(data || [])
    } catch (error) {
      console.error('Error fetching recipes:', error)
    }
  }

  const addItem = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { error } = await supabase.from('shopping_list_items').insert([
        {
          ...newItem,
          shopping_list_id: params.id as string,
          unit: newItem.unit || null,
          notes: newItem.notes || null,
          estimated_price: newItem.estimated_price ? parseFloat(newItem.estimated_price) : null,
          category: newItem.category || null,
          recipe_id: newItem.recipe_id || null,
          store_section: newItem.store_section || null,
          brand_preference: newItem.brand_preference || null,
        },
      ])

      if (error) throw error

      setNewItem({
        name: '',
        quantity: 1,
        unit: '',
        notes: '',
        estimated_price: '',
        category: '',
        priority: 1,
        recipe_id: '',
        store_section: '',
        brand_preference: '',
        is_organic: false,
      })
      setShowAddForm(false)
      toast.success('Item added successfully!')
      fetchShoppingListData()
    } catch (error) {
      console.error('Error adding item:', error)
      toast.error('Failed to add item. Please try again.')
    }
  }

  const addBulkItems = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const itemLines = bulkItems.split('\n').filter(line => line.trim())
      const itemsToAdd = itemLines.map(line => {
        const parts = line.trim().split(',').map(part => part.trim())
        const name = parts[0]
        const quantity = parts[1] ? parseInt(parts[1]) || 1 : 1
        const unit = parts[2] || null

        return {
          shopping_list_id: params.id as string,
          name,
          quantity,
          unit,
          priority: 2, // Medium priority for bulk items
        }
      })

      const { error } = await supabase.from('shopping_list_items').insert(itemsToAdd)

      if (error) throw error

      setBulkItems('')
      setShowBulkAdd(false)
      toast.success(`${itemsToAdd.length} items added successfully!`)
      fetchShoppingListData()
    } catch (error) {
      console.error('Error adding bulk items:', error)
      toast.error('Failed to add items. Please try again.')
    }
  }

  const toggleItem = async (itemId: string, completed: boolean) => {
    try {
      const { error } = await supabase
        .from('shopping_list_items')
        .update({ completed: !completed })
        .eq('id', itemId)

      if (error) throw error
      fetchShoppingListData()
    } catch (error) {
      console.error('Error updating item:', error)
      toast.error('Failed to update item. Please try again.')
    }
  }

  const updateItemPrice = async (itemId: string, actualPrice: number) => {
    try {
      const { error } = await supabase
        .from('shopping_list_items')
        .update({ actual_price: actualPrice })
        .eq('id', itemId)

      if (error) throw error
      toast.success('Price updated successfully!')
      fetchShoppingListData()
    } catch (error) {
      console.error('Error updating price:', error)
      toast.error('Failed to update price. Please try again.')
    }
  }

  const deleteItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return

    try {
      const { error } = await supabase
        .from('shopping_list_items')
        .delete()
        .eq('id', itemId)

      if (error) throw error
      toast.success('Item deleted successfully!')
      fetchShoppingListData()
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.error('Failed to delete item. Please try again.')
    }
  }

  const getFilteredAndSortedItems = () => {
    let filtered = items

    if (filterCategory !== 'all') {
      filtered = items.filter(item => item.category === filterCategory)
    }

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'category':
          return (a.category || '').localeCompare(b.category || '')
        case 'priority':
          return a.priority - b.priority
        case 'price':
          return (b.estimated_price || 0) - (a.estimated_price || 0)
        default:
          return 0
      }
    })
  }

  const calculateTotals = () => {
    const totalItems = items.length
    const completedItems = items.filter(item => item.completed).length
    const estimatedTotal = items.reduce((sum, item) => sum + ((item.estimated_price || 0) * item.quantity), 0)
    const actualTotal = items.reduce((sum, item) => sum + ((item.actual_price || 0) * item.quantity), 0)

    return { totalItems, completedItems, estimatedTotal, actualTotal }
  }

  const getUniqueCategories = () => {
    const categories = items.map(item => item.category).filter(Boolean)
    return [...new Set(categories)]
  }

  const completedItems = items.filter(item => item.completed)
  const pendingItems = getFilteredAndSortedItems().filter(item => !item.completed)
  const totals = calculateTotals()

  if (loading) {
    return <Loading size="lg" text="Loading shopping list..." className="h-64" />
  }

  if (!shoppingList) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Shopping list not found</h3>
        <Link href="/shopping" className="text-blue-600 hover:text-blue-500">
          Back to shopping lists
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/shopping">
            <Button variant="outline" size="sm">
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{shoppingList.name}</h1>
            <div className="flex items-center space-x-4 mt-2">
              {shoppingList.store_name && (
                <div className="flex items-center text-sm text-gray-600">
                  <MapPinIcon className="h-4 w-4 mr-1" />
                  {shoppingList.store_name}
                </div>
              )}
              {shoppingList.is_shared && (
                <Badge variant="secondary">
                  <UsersIcon className="h-3 w-3 mr-1" />
                  Shared
                </Badge>
              )}
            </div>
            {shoppingList.description && (
              <p className="mt-2 text-gray-600">{shoppingList.description}</p>
            )}
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowBulkAdd(true)}
            variant="outline"
          >
            Bulk Add
          </Button>
          <Button onClick={() => setShowAddForm(true)}>
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{totals.totalItems}</p>
              </div>
              <ShoppingCartIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-green-600">{totals.completedItems}</p>
              </div>
              <CheckIcon className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Estimated Cost</p>
                <p className="text-2xl font-bold text-blue-600">${totals.estimatedTotal.toFixed(2)}</p>
              </div>
              <DollarSignIcon className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Actual Cost</p>
                <p className="text-2xl font-bold text-purple-600">${totals.actualTotal.toFixed(2)}</p>
              </div>
              <TrendingUpIcon className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Shopping Progress</span>
            <span className="text-sm text-gray-600">
              {totals.totalItems > 0 ? Math.round((totals.completedItems / totals.totalItems) * 100) : 0}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-green-600 h-3 rounded-full transition-all duration-300"
              style={{
                width: `${totals.totalItems > 0 ? (totals.completedItems / totals.totalItems) * 100 : 0}%`
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Filters and Sorting */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sort by
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="priority">Priority</option>
                <option value="name">Name</option>
                <option value="category">Category</option>
                <option value="price">Price</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Category
              </label>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="all">All Categories</option>
                {getUniqueCategories().map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={() => {
                  setSortBy('priority')
                  setFilterCategory('all')
                }}
                className="w-full"
              >
                Reset Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Item Form */}
      {showAddForm && (
        <div className="mb-6 bg-white shadow rounded-lg p-6">
          <form onSubmit={addItem}>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="sm:col-span-2">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Item Name
                </label>
                <input
                  type="text"
                  id="name"
                  required
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  placeholder="e.g., Milk, Bread, Apples"
                />
              </div>
              <div>
                <label htmlFor="quantity" className="block text-sm font-medium text-gray-700">
                  Quantity
                </label>
                <input
                  type="number"
                  id="quantity"
                  min="1"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newItem.quantity}
                  onChange={(e) => setNewItem({ ...newItem, quantity: parseInt(e.target.value) || 1 })}
                />
              </div>
              <div>
                <label htmlFor="unit" className="block text-sm font-medium text-gray-700">
                  Unit (Optional)
                </label>
                <input
                  type="text"
                  id="unit"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newItem.unit}
                  onChange={(e) => setNewItem({ ...newItem, unit: e.target.value })}
                  placeholder="e.g., lbs, oz, bottles"
                />
              </div>
              <div className="sm:col-span-2">
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700">
                  Notes (Optional)
                </label>
                <input
                  type="text"
                  id="notes"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  value={newItem.notes}
                  onChange={(e) => setNewItem({ ...newItem, notes: e.target.value })}
                  placeholder="e.g., organic, brand preference"
                />
              </div>
            </div>
            <div className="mt-4 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Add Item
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Shopping List Items */}
      <div className="space-y-6">
        {/* Pending Items */}
        {pendingItems.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Shopping List ({pendingItems.length} items)
              </h3>
            </div>
            <ul className="divide-y divide-gray-200">
              {pendingItems.map((item) => (
                <li key={item.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleItem(item.id, item.completed)}
                        className="flex-shrink-0 h-5 w-5 rounded border-2 border-gray-300 hover:border-blue-500 flex items-center justify-center"
                      >
                        {item.completed && <CheckIcon className="h-3 w-3 text-blue-600" />}
                      </button>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          {item.quantity} {item.unit && `${item.unit} `}{item.name}
                        </p>
                        {item.notes && (
                          <p className="text-sm text-gray-500">{item.notes}</p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => deleteItem(item.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <XIcon className="h-5 w-5" />
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Completed Items */}
        {completedItems.length > 0 && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">
                Completed ({completedItems.length} items)
              </h3>
            </div>
            <ul className="divide-y divide-gray-200">
              {completedItems.map((item) => (
                <li key={item.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleItem(item.id, item.completed)}
                        className="flex-shrink-0 h-5 w-5 rounded border-2 bg-blue-600 border-blue-600 flex items-center justify-center"
                      >
                        <CheckIcon className="h-3 w-3 text-white" />
                      </button>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-500 line-through">
                          {item.quantity} {item.unit && `${item.unit} `}{item.name}
                        </p>
                        {item.notes && (
                          <p className="text-sm text-gray-400 line-through">{item.notes}</p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => deleteItem(item.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <XIcon className="h-5 w-5" />
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {items.length === 0 && (
          <div className="text-center py-12">
            <h3 className="mt-2 text-sm font-medium text-gray-900">No items yet</h3>
            <p className="mt-1 text-sm text-gray-500">Add your first item to get started.</p>
          </div>
        )}
      </div>
    </div>
  )
}
