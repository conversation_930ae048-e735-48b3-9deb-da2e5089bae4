{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Card.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border border-gray-200 bg-white text-gray-950 shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm',\n        className\n      )}\n      {...props}\n    />\n  )\n)\nCard.displayName = 'Card'\n\nconst CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n  )\n)\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}\n      {...props}\n    />\n  )\n)\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p ref={ref} className={cn('text-sm text-gray-600', className)} {...props} />\n  )\n)\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n  )\n)\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n  )\n)\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAIf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAGvF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;AAG7E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGlE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACxB,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAGpF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,8OAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,8OAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;AAEO,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/budget/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { PlusIcon, DollarSignIcon, TrendingUpIcon, TrendingDownIcon, SearchIcon, FilterIcon, EditIcon, TrashIcon, CalendarIcon, AlertTriangleIcon, BarChart3Icon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { Modal } from '@/components/ui/Modal'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts'\n\ntype Transaction = Database['public']['Tables']['transactions']['Row']\ntype BudgetCategory = Database['public']['Tables']['budget_categories']['Row']\n\nconst transactionSchema = z.object({\n  amount: z.number().min(0.01, 'Amount must be greater than 0'),\n  description: z.string().min(1, 'Description is required').max(200, 'Description too long'),\n  category_id: z.string().min(1, 'Category is required'),\n  type: z.enum(['income', 'expense']),\n  date: z.string().min(1, 'Date is required'),\n})\n\nconst categorySchema = z.object({\n  name: z.string().min(1, 'Category name is required').max(50, 'Name too long'),\n  budget_limit: z.number().min(0, 'Budget limit must be positive').optional(),\n  color: z.string().optional(),\n  type: z.enum(['income', 'expense']),\n})\n\ntype TransactionFormData = z.infer<typeof transactionSchema>\ntype CategoryFormData = z.infer<typeof categorySchema>\n\nexport default function BudgetPage() {\n  const [transactions, setTransactions] = useState<Transaction[]>([])\n  const [categories, setCategories] = useState<BudgetCategory[]>([])\n  const [loading, setLoading] = useState(true)\n  const [showAddTransaction, setShowAddTransaction] = useState(false)\n  const [showAddCategory, setShowAddCategory] = useState(false)\n  const [editingTransaction, setEditingTransaction] = useState<Transaction | null>(null)\n  const [editingCategory, setEditingCategory] = useState<BudgetCategory | null>(null)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all')\n  const [filterCategory, setFilterCategory] = useState<string>('all')\n  const [dateRange, setDateRange] = useState({\n    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [newTransaction, setNewTransaction] = useState({\n    amount: '',\n    description: '',\n    date: new Date().toISOString().split('T')[0],\n    type: 'expense' as 'income' | 'expense',\n    category_id: '',\n  })\n  const [newCategory, setNewCategory] = useState({\n    name: '',\n    type: 'expense' as 'income' | 'expense',\n    color: '#3B82F6',\n    budget_limit: undefined as number | undefined,\n  })\n  const supabase = createClient()\n\n  useEffect(() => {\n    fetchData()\n\n    // Set up real-time subscriptions\n    const transactionsSubscription = supabase\n      .channel('transactions_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'transactions' },\n        (payload) => {\n          console.log('Transaction change received:', payload)\n          fetchData() // Refetch data when changes occur\n        }\n      )\n      .subscribe()\n\n    const categoriesSubscription = supabase\n      .channel('categories_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'budget_categories' },\n        (payload) => {\n          console.log('Category change received:', payload)\n          fetchData() // Refetch data when changes occur\n        }\n      )\n      .subscribe()\n\n    // Cleanup subscriptions on unmount\n    return () => {\n      supabase.removeChannel(transactionsSubscription)\n      supabase.removeChannel(categoriesSubscription)\n    }\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      const [transactionsResult, categoriesResult] = await Promise.all([\n        supabase\n          .from('transactions')\n          .select('*, budget_categories(name, color)')\n          .order('date', { ascending: false })\n          .limit(50),\n        supabase\n          .from('budget_categories')\n          .select('*')\n          .order('name'),\n      ])\n\n      if (transactionsResult.error) throw transactionsResult.error\n      if (categoriesResult.error) throw categoriesResult.error\n\n      setTransactions(transactionsResult.data || [])\n      setCategories(categoriesResult.data || [])\n    } catch (error) {\n      console.error('Error fetching data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addTransaction = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('transactions').insert([\n        {\n          ...newTransaction,\n          amount: parseFloat(newTransaction.amount),\n          user_id: user.id,\n          category_id: newTransaction.category_id || null,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewTransaction({\n        amount: '',\n        description: '',\n        date: new Date().toISOString().split('T')[0],\n        type: 'expense',\n        category_id: '',\n      })\n      setShowAddTransaction(false)\n      toast.success('Transaction added successfully!')\n      fetchData()\n    } catch (error) {\n      console.error('Error adding transaction:', error)\n      toast.error('Failed to add transaction. Please try again.')\n    }\n  }\n\n  const addCategory = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { error } = await supabase.from('budget_categories').insert([\n        {\n          ...newCategory,\n          user_id: user.id,\n        },\n      ])\n\n      if (error) throw error\n\n      setNewCategory({\n        name: '',\n        type: 'expense',\n        color: '#3B82F6',\n        budget_limit: undefined,\n      })\n      setShowAddCategory(false)\n      toast.success('Category added successfully!')\n      fetchData()\n    } catch (error) {\n      console.error('Error adding category:', error)\n      toast.error('Failed to add category. Please try again.')\n    }\n  }\n\n  const calculateTotals = () => {\n    const currentMonth = new Date().toISOString().slice(0, 7)\n    const monthlyTransactions = transactions.filter(t =>\n      t.date.startsWith(currentMonth)\n    )\n\n    const income = monthlyTransactions\n      .filter(t => t.type === 'income')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    const expenses = monthlyTransactions\n      .filter(t => t.type === 'expense')\n      .reduce((sum, t) => sum + t.amount, 0)\n\n    return { income, expenses, net: income - expenses }\n  }\n\n  const getCategorySpending = () => {\n    const currentMonth = new Date().toISOString().slice(0, 7)\n    const monthlyExpenses = transactions.filter(t =>\n      t.date.startsWith(currentMonth) && t.type === 'expense'\n    )\n\n    const categorySpending = categories\n      .filter(cat => cat.type === 'expense')\n      .map(category => {\n        const spent = monthlyExpenses\n          .filter(t => t.category_id === category.id)\n          .reduce((sum, t) => sum + t.amount, 0)\n\n        return {\n          name: category.name,\n          spent,\n          budget: category.budget_limit || 0,\n          color: category.color,\n          percentage: category.budget_limit ? (spent / category.budget_limit) * 100 : 0,\n          isOverBudget: category.budget_limit ? spent > category.budget_limit : false\n        }\n      })\n      .filter(cat => cat.spent > 0 || cat.budget > 0)\n\n    return categorySpending\n  }\n\n  const getMonthlyTrend = () => {\n    const last6Months = []\n    const now = new Date()\n\n    for (let i = 5; i >= 0; i--) {\n      const date = new Date(now.getFullYear(), now.getMonth() - i, 1)\n      const monthStr = date.toISOString().slice(0, 7)\n      const monthTransactions = transactions.filter(t => t.date.startsWith(monthStr))\n\n      const income = monthTransactions\n        .filter(t => t.type === 'income')\n        .reduce((sum, t) => sum + t.amount, 0)\n\n      const expenses = monthTransactions\n        .filter(t => t.type === 'expense')\n        .reduce((sum, t) => sum + t.amount, 0)\n\n      last6Months.push({\n        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),\n        income,\n        expenses,\n        net: income - expenses\n      })\n    }\n\n    return last6Months\n  }\n\n  const getBudgetAlerts = () => {\n    const categorySpending = getCategorySpending()\n    return categorySpending.filter(cat => cat.isOverBudget || cat.percentage > 80)\n  }\n\n  const getFilteredTransactions = () => {\n    return transactions.filter(transaction => {\n      // Search filter\n      if (searchTerm && !transaction.description.toLowerCase().includes(searchTerm.toLowerCase())) {\n        return false\n      }\n\n      // Type filter\n      if (filterType !== 'all' && transaction.type !== filterType) {\n        return false\n      }\n\n      // Category filter\n      if (filterCategory !== 'all' && transaction.category_id !== filterCategory) {\n        return false\n      }\n\n      // Date range filter\n      if (transaction.date < dateRange.start || transaction.date > dateRange.end) {\n        return false\n      }\n\n      return true\n    })\n  }\n\n  const totals = calculateTotals()\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading your budget data...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Budget</h1>\n          <p className=\"mt-2 text-gray-600\">\n            Track your income and expenses with detailed insights\n          </p>\n        </div>\n        <div className=\"flex flex-col sm:flex-row gap-3\">\n          <Button\n            onClick={() => setShowAddCategory(true)}\n            variant=\"outline\"\n          >\n            Add Category\n          </Button>\n          <Button\n            onClick={() => setShowAddTransaction(true)}\n            size=\"lg\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            Add Transaction\n          </Button>\n        </div>\n      </div>\n\n      {/* Monthly Summary */}\n      <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-3\">\n        <Card className=\"bg-gradient-to-br from-green-50 to-emerald-50 border-green-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-green-700\">Monthly Income</p>\n                <p className=\"text-2xl font-bold text-green-900\">\n                  ${totals.income.toFixed(2)}\n                </p>\n              </div>\n              <div className=\"p-3 bg-green-100 rounded-full\">\n                <TrendingUpIcon className=\"h-6 w-6 text-green-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className=\"bg-gradient-to-br from-red-50 to-rose-50 border-red-200\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-red-700\">Monthly Expenses</p>\n                <p className=\"text-2xl font-bold text-red-900\">\n                  ${totals.expenses.toFixed(2)}\n                </p>\n              </div>\n              <div className=\"p-3 bg-red-100 rounded-full\">\n                <TrendingDownIcon className=\"h-6 w-6 text-red-600\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card className={`bg-gradient-to-br ${\n          totals.net >= 0\n            ? 'from-blue-50 to-indigo-50 border-blue-200'\n            : 'from-orange-50 to-red-50 border-orange-200'\n        }`}>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className={`text-sm font-medium ${\n                  totals.net >= 0 ? 'text-blue-700' : 'text-orange-700'\n                }`}>\n                  Net Income\n                </p>\n                <p className={`text-2xl font-bold ${\n                  totals.net >= 0 ? 'text-blue-900' : 'text-orange-900'\n                }`}>\n                  ${totals.net.toFixed(2)}\n                </p>\n              </div>\n              <div className={`p-3 rounded-full ${\n                totals.net >= 0 ? 'bg-blue-100' : 'bg-orange-100'\n              }`}>\n                <DollarSignIcon className={`h-6 w-6 ${\n                  totals.net >= 0 ? 'text-blue-600' : 'text-orange-600'\n                }`} />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Budget Alerts */}\n      {getBudgetAlerts().length > 0 && (\n        <Card className=\"bg-gradient-to-br from-orange-50 to-red-50 border-orange-200\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2 text-orange-800\">\n              <AlertTriangleIcon className=\"h-5 w-5\" />\n              Budget Alerts\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {getBudgetAlerts().map((category, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200\">\n                  <div className=\"flex items-center gap-3\">\n                    <div\n                      className=\"w-3 h-3 rounded-full\"\n                      style={{ backgroundColor: category.color }}\n                    />\n                    <div>\n                      <p className=\"font-medium text-gray-900\">{category.name}</p>\n                      <p className=\"text-sm text-gray-600\">\n                        ${category.spent.toFixed(2)} of ${category.budget.toFixed(2)} spent\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <Badge variant={category.isOverBudget ? \"destructive\" : \"secondary\"}>\n                      {category.percentage.toFixed(0)}%\n                    </Badge>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Monthly Trend Chart */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <BarChart3Icon className=\"h-5 w-5\" />\n              6-Month Trend\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={getMonthlyTrend()}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, '']} />\n                <Line type=\"monotone\" dataKey=\"income\" stroke=\"#10B981\" strokeWidth={2} name=\"Income\" />\n                <Line type=\"monotone\" dataKey=\"expenses\" stroke=\"#EF4444\" strokeWidth={2} name=\"Expenses\" />\n                <Line type=\"monotone\" dataKey=\"net\" stroke=\"#3B82F6\" strokeWidth={2} name=\"Net\" />\n              </LineChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n\n        {/* Category Spending Chart */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Category Spending</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={getCategorySpending()}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  outerRadius={80}\n                  dataKey=\"spent\"\n                  label={({ name, spent }) => `${name}: $${spent.toFixed(0)}`}\n                >\n                  {getCategorySpending().map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={entry.color} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value) => [`$${Number(value).toFixed(2)}`, 'Spent']} />\n              </PieChart>\n            </ResponsiveContainer>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Budget Progress Bars */}\n      {getCategorySpending().filter(cat => cat.budget > 0).length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Budget Progress</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {getCategorySpending()\n                .filter(cat => cat.budget > 0)\n                .map((category, index) => (\n                  <div key={index} className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <div className=\"flex items-center gap-2\">\n                        <div\n                          className=\"w-3 h-3 rounded-full\"\n                          style={{ backgroundColor: category.color }}\n                        />\n                        <span className=\"font-medium\">{category.name}</span>\n                      </div>\n                      <span className=\"text-sm text-gray-600\">\n                        ${category.spent.toFixed(2)} / ${category.budget.toFixed(2)}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full transition-all duration-300 ${\n                          category.isOverBudget ? 'bg-red-500' :\n                          category.percentage > 80 ? 'bg-orange-500' : 'bg-green-500'\n                        }`}\n                        style={{ width: `${Math.min(category.percentage, 100)}%` }}\n                      />\n                    </div>\n                  </div>\n                ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Add Category Form */}\n      {showAddCategory && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Category</h3>\n          <form onSubmit={addCategory}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n              <div>\n                <label htmlFor=\"categoryName\" className=\"block text-sm font-medium text-gray-700\">\n                  Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"categoryName\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.name}\n                  onChange={(e) => setNewCategory({ ...newCategory, name: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"categoryType\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"categoryType\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.type}\n                  onChange={(e) => setNewCategory({ ...newCategory, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"categoryBudgetLimit\" className=\"block text-sm font-medium text-gray-700\">\n                  Budget Limit (Optional)\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"categoryBudgetLimit\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newCategory.budget_limit || ''}\n                  onChange={(e) => setNewCategory({ ...newCategory, budget_limit: e.target.value ? parseFloat(e.target.value) : undefined })}\n                  placeholder=\"No limit\"\n                />\n              </div>\n              <div>\n                <label htmlFor=\"categoryColor\" className=\"block text-sm font-medium text-gray-700\">\n                  Color\n                </label>\n                <input\n                  type=\"color\"\n                  id=\"categoryColor\"\n                  className=\"mt-1 block w-full h-10 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500\"\n                  value={newCategory.color}\n                  onChange={(e) => setNewCategory({ ...newCategory, color: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddCategory(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Category\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Add Transaction Form */}\n      {showAddTransaction && (\n        <div className=\"mb-6 bg-white shadow rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add Transaction</h3>\n          <form onSubmit={addTransaction}>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2\">\n              <div>\n                <label htmlFor=\"amount\" className=\"block text-sm font-medium text-gray-700\">\n                  Amount\n                </label>\n                <input\n                  type=\"number\"\n                  id=\"amount\"\n                  step=\"0.01\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.amount}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, amount: e.target.value })}\n                />\n              </div>\n              <div>\n                <label htmlFor=\"type\" className=\"block text-sm font-medium text-gray-700\">\n                  Type\n                </label>\n                <select\n                  id=\"type\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.type}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, type: e.target.value as 'income' | 'expense' })}\n                >\n                  <option value=\"expense\">Expense</option>\n                  <option value=\"income\">Income</option>\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700\">\n                  Category\n                </label>\n                <select\n                  id=\"category\"\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.category_id}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, category_id: e.target.value })}\n                >\n                  <option value=\"\">No category</option>\n                  {categories\n                    .filter(cat => cat.type === newTransaction.type)\n                    .map((category) => (\n                      <option key={category.id} value={category.id}>\n                        {category.name}\n                      </option>\n                    ))}\n                </select>\n              </div>\n              <div>\n                <label htmlFor=\"date\" className=\"block text-sm font-medium text-gray-700\">\n                  Date\n                </label>\n                <input\n                  type=\"date\"\n                  id=\"date\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.date}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, date: e.target.value })}\n                />\n              </div>\n              <div className=\"sm:col-span-2\">\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">\n                  Description\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"description\"\n                  required\n                  className=\"mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                  value={newTransaction.description}\n                  onChange={(e) => setNewTransaction({ ...newTransaction, description: e.target.value })}\n                />\n              </div>\n            </div>\n            <div className=\"mt-4 flex justify-end space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => setShowAddTransaction(false)}\n                className=\"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                type=\"submit\"\n                className=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700\"\n              >\n                Add Transaction\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Filters and Search */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Filter Transactions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label htmlFor=\"search\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Search\n              </label>\n              <div className=\"relative\">\n                <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  id=\"search\"\n                  type=\"text\"\n                  placeholder=\"Search transactions...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div>\n              <label htmlFor=\"filterType\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Type\n              </label>\n              <select\n                id=\"filterType\"\n                value={filterType}\n                onChange={(e) => setFilterType(e.target.value as 'all' | 'income' | 'expense')}\n                className=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"all\">All Types</option>\n                <option value=\"income\">Income</option>\n                <option value=\"expense\">Expense</option>\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"filterCategory\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Category\n              </label>\n              <select\n                id=\"filterCategory\"\n                value={filterCategory}\n                onChange={(e) => setFilterCategory(e.target.value)}\n                className=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n              >\n                <option value=\"all\">All Categories</option>\n                {categories.map((category) => (\n                  <option key={category.id} value={category.id}>\n                    {category.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label htmlFor=\"dateRange\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Date Range\n              </label>\n              <div className=\"flex gap-2\">\n                <input\n                  type=\"date\"\n                  value={dateRange.start}\n                  onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}\n                  className=\"flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n                <input\n                  type=\"date\"\n                  value={dateRange.end}\n                  onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}\n                  className=\"flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Recent Transactions */}\n      <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n        <div className=\"px-4 py-5 sm:px-6 flex justify-between items-center\">\n          <h3 className=\"text-lg leading-6 font-medium text-gray-900\">\n            Recent Transactions\n          </h3>\n          <Badge variant=\"secondary\">\n            {getFilteredTransactions().length} transactions\n          </Badge>\n        </div>\n        {getFilteredTransactions().length === 0 ? (\n          <div className=\"text-center py-12\">\n            <DollarSignIcon className=\"mx-auto h-12 w-12 text-gray-400\" />\n            <h3 className=\"mt-2 text-sm font-medium text-gray-900\">\n              {transactions.length === 0 ? 'No transactions' : 'No transactions match your filters'}\n            </h3>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {transactions.length === 0\n                ? 'Get started by adding your first transaction.'\n                : 'Try adjusting your search or filter criteria.'}\n            </p>\n          </div>\n        ) : (\n          <ul className=\"divide-y divide-gray-200\">\n            {getFilteredTransactions().map((transaction) => (\n              <li key={transaction.id} className=\"px-6 py-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <div className={`flex-shrink-0 h-3 w-3 rounded-full`} \n                         style={{ backgroundColor: (transaction as any).budget_categories?.color || '#6B7280' }} />\n                    <div className=\"ml-3\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {transaction.description}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {new Date(transaction.date).toLocaleDateString()} • {(transaction as any).budget_categories?.name || 'No category'}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <span className={`text-sm font-medium ${\n                      transaction.type === 'income' ? 'text-green-600' : 'text-red-600'\n                    }`}>\n                      {transaction.type === 'income' ? '+' : '-'}${transaction.amount.toFixed(2)}\n                    </span>\n                  </div>\n                </div>\n              </li>\n            ))}\n          </ul>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AAGA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjBA;;;;;;;;;;;;;AAsBA,MAAM,oBAAoB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,QAAQ,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;IAC7B,aAAa,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC,KAAK;IACnE,aAAa,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,MAAM,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;IAClC,MAAM,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC1B;AAEA,MAAM,iBAAiB,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,6BAA6B,GAAG,CAAC,IAAI;IAC7D,cAAc,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,iCAAiC,QAAQ;IACzE,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,MAAM,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAU;AACpC;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACjF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,OAAO,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/F,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;QACR,aAAa;QACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,MAAM;QACN,aAAa;IACf;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM;QACN,MAAM;QACN,OAAO;QACP,cAAc;IAChB;IACA,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,iCAAiC;QACjC,MAAM,2BAA2B,SAC9B,OAAO,CAAC,wBACR,EAAE,CAAC,oBACF;YAAE,OAAO;YAAK,QAAQ;YAAU,OAAO;QAAe,GACtD,CAAC;YACC,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,YAAY,kCAAkC;;QAChD,GAED,SAAS;QAEZ,MAAM,yBAAyB,SAC5B,OAAO,CAAC,sBACR,EAAE,CAAC,oBACF;YAAE,OAAO;YAAK,QAAQ;YAAU,OAAO;QAAoB,GAC3D,CAAC;YACC,QAAQ,GAAG,CAAC,6BAA6B;YACzC,YAAY,kCAAkC;;QAChD,GAED,SAAS;QAEZ,mCAAmC;QACnC,OAAO;YACL,SAAS,aAAa,CAAC;YACvB,SAAS,aAAa,CAAC;QACzB;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,CAAC,oBAAoB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/D,SACG,IAAI,CAAC,gBACL,MAAM,CAAC,qCACP,KAAK,CAAC,QAAQ;oBAAE,WAAW;gBAAM,GACjC,KAAK,CAAC;gBACT,SACG,IAAI,CAAC,qBACL,MAAM,CAAC,KACP,KAAK,CAAC;aACV;YAED,IAAI,mBAAmB,KAAK,EAAE,MAAM,mBAAmB,KAAK;YAC5D,IAAI,iBAAiB,KAAK,EAAE,MAAM,iBAAiB,KAAK;YAExD,gBAAgB,mBAAmB,IAAI,IAAI,EAAE;YAC7C,cAAc,iBAAiB,IAAI,IAAI,EAAE;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,gBAAgB,MAAM,CAAC;gBAC3D;oBACE,GAAG,cAAc;oBACjB,QAAQ,WAAW,eAAe,MAAM;oBACxC,SAAS,KAAK,EAAE;oBAChB,aAAa,eAAe,WAAW,IAAI;gBAC7C;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,kBAAkB;gBAChB,QAAQ;gBACR,aAAa;gBACb,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC5C,MAAM;gBACN,aAAa;YACf;YACA,sBAAsB;YACtB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,MAAM,CAAC;gBAChE;oBACE,GAAG,WAAW;oBACd,SAAS,KAAK,EAAE;gBAClB;aACD;YAED,IAAI,OAAO,MAAM;YAEjB,eAAe;gBACb,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,cAAc;YAChB;YACA,mBAAmB;YACnB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,MAAM,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG;QACvD,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAA,IAC9C,EAAE,IAAI,CAAC,UAAU,CAAC;QAGpB,MAAM,SAAS,oBACZ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,WAAW,oBACd,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,OAAO;YAAE;YAAQ;YAAU,KAAK,SAAS;QAAS;IACpD;IAEA,MAAM,sBAAsB;QAC1B,MAAM,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,GAAG;QACvD,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA,IAC1C,EAAE,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,IAAI,KAAK;QAGhD,MAAM,mBAAmB,WACtB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,WAC3B,GAAG,CAAC,CAAA;YACH,MAAM,QAAQ,gBACX,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,SAAS,EAAE,EACzC,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAEtC,OAAO;gBACL,MAAM,SAAS,IAAI;gBACnB;gBACA,QAAQ,SAAS,YAAY,IAAI;gBACjC,OAAO,SAAS,KAAK;gBACrB,YAAY,SAAS,YAAY,GAAG,AAAC,QAAQ,SAAS,YAAY,GAAI,MAAM;gBAC5E,cAAc,SAAS,YAAY,GAAG,QAAQ,SAAS,YAAY,GAAG;YACxE;QACF,GACC,MAAM,CAAC,CAAA,MAAO,IAAI,KAAK,GAAG,KAAK,IAAI,MAAM,GAAG;QAE/C,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,cAAc,EAAE;QACtB,MAAM,MAAM,IAAI;QAEhB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,OAAO,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG;YAC7D,MAAM,WAAW,KAAK,WAAW,GAAG,KAAK,CAAC,GAAG;YAC7C,MAAM,oBAAoB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,UAAU,CAAC;YAErE,MAAM,SAAS,kBACZ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAEtC,MAAM,WAAW,kBACd,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WACvB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;YAEtC,YAAY,IAAI,CAAC;gBACf,OAAO,KAAK,kBAAkB,CAAC,SAAS;oBAAE,OAAO;oBAAS,MAAM;gBAAU;gBAC1E;gBACA;gBACA,KAAK,SAAS;YAChB;QACF;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,mBAAmB;QACzB,OAAO,iBAAiB,MAAM,CAAC,CAAA,MAAO,IAAI,YAAY,IAAI,IAAI,UAAU,GAAG;IAC7E;IAEA,MAAM,0BAA0B;QAC9B,OAAO,aAAa,MAAM,CAAC,CAAA;YACzB,gBAAgB;YAChB,IAAI,cAAc,CAAC,YAAY,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAAK;gBAC3F,OAAO;YACT;YAEA,cAAc;YACd,IAAI,eAAe,SAAS,YAAY,IAAI,KAAK,YAAY;gBAC3D,OAAO;YACT;YAEA,kBAAkB;YAClB,IAAI,mBAAmB,SAAS,YAAY,WAAW,KAAK,gBAAgB;gBAC1E,OAAO;YACT;YAEA,oBAAoB;YACpB,IAAI,YAAY,IAAI,GAAG,UAAU,KAAK,IAAI,YAAY,IAAI,GAAG,UAAU,GAAG,EAAE;gBAC1E,OAAO;YACT;YAEA,OAAO;QACT;IACF;IAEA,MAAM,SAAS;IAEf,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAA8B,WAAU;;;;;;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,mBAAmB;gCAClC,SAAQ;0CACT;;;;;;0CAGD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,sBAAsB;gCACrC,MAAK;;kDAEL,8OAAC,sMAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;;oDAAoC;oDAC7C,OAAO,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAmC;;;;;;0DAChD,8OAAC;gDAAE,WAAU;;oDAAkC;oDAC3C,OAAO,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAG9B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMpC,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAW,CAAC,kBAAkB,EAClC,OAAO,GAAG,IAAI,IACV,8CACA,8CACJ;kCACA,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAW,CAAC,oBAAoB,EACjC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;0DAAE;;;;;;0DAGJ,8OAAC;gDAAE,WAAW,CAAC,mBAAmB,EAChC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;;oDAAE;oDACA,OAAO,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAGzB,8OAAC;wCAAI,WAAW,CAAC,iBAAiB,EAChC,OAAO,GAAG,IAAI,IAAI,gBAAgB,iBAClC;kDACA,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,WAAW,CAAC,QAAQ,EAClC,OAAO,GAAG,IAAI,IAAI,kBAAkB,mBACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQX,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,4NAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAI7C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,SAAS,KAAK;oDAAC;;;;;;8DAE3C,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B,SAAS,IAAI;;;;;;sEACvD,8OAAC;4DAAE,WAAU;;gEAAwB;gEACjC,SAAS,KAAK,CAAC,OAAO,CAAC;gEAAG;gEAAM,SAAS,MAAM,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;;;;;;;sDAInE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,SAAS,YAAY,GAAG,gBAAgB;;oDACrD,SAAS,UAAU,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;mCAf5B;;;;;;;;;;;;;;;;;;;;;0BA0BpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,sNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIzC,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wCAAC,MAAM;;0DACf,8OAAC,6JAAA,CAAA,gBAAa;gDAAC,iBAAgB;;;;;;0DAC/B,8OAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;;;;;;0DACf,8OAAC,qJAAA,CAAA,QAAK;;;;;0DACN,8OAAC,uJAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,CAAC,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,IAAI;wDAAE;qDAAG;;;;;;0DACnE,8OAAC,oJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAS,QAAO;gDAAU,aAAa;gDAAG,MAAK;;;;;;0DAC7E,8OAAC,oJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAW,QAAO;gDAAU,aAAa;gDAAG,MAAK;;;;;;0DAC/E,8OAAC,oJAAA,CAAA,OAAI;gDAAC,MAAK;gDAAW,SAAQ;gDAAM,QAAO;gDAAU,aAAa;gDAAG,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlF,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAQ;8CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;0DACP,8OAAC,+IAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,SAAQ;gDACR,OAAO,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAK,GAAG,KAAK,GAAG,EAAE,MAAM,OAAO,CAAC,IAAI;0DAE1D,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,8OAAC,oJAAA,CAAA,OAAI;wDAAuB,MAAM,MAAM,KAAK;uDAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,8OAAC,uJAAA,CAAA,UAAO;gDAAC,WAAW,CAAC,QAAU;wDAAC,CAAC,CAAC,EAAE,OAAO,OAAO,OAAO,CAAC,IAAI;wDAAE;qDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQjF,sBAAsB,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,GAAG,MAAM,GAAG,mBAC5D,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;sCACZ,sBACE,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,GAC3B,GAAG,CAAC,CAAC,UAAU,sBACd,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,SAAS,KAAK;4DAAC;;;;;;sEAE3C,8OAAC;4DAAK,WAAU;sEAAe,SAAS,IAAI;;;;;;;;;;;;8DAE9C,8OAAC;oDAAK,WAAU;;wDAAwB;wDACpC,SAAS,KAAK,CAAC,OAAO,CAAC;wDAAG;wDAAK,SAAS,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;sDAG7D,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,6CAA6C,EACvD,SAAS,YAAY,GAAG,eACxB,SAAS,UAAU,GAAG,KAAK,kBAAkB,gBAC7C;gDACF,OAAO;oDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,SAAS,UAAU,EAAE,KAAK,CAAC,CAAC;gDAAC;;;;;;;;;;;;mCAnBrD;;;;;;;;;;;;;;;;;;;;;YA8BrB,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAG3E,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAA0C;;;;;;0DAGlF,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,IAAI;gDACvB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAE/F,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAsB,WAAU;0DAA0C;;;;;;0DAGzF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,KAAI;gDACJ,WAAU;gDACV,OAAO,YAAY,YAAY,IAAI;gDACnC,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK,GAAG,WAAW,EAAE,MAAM,CAAC,KAAK,IAAI;oDAAU;gDACxH,aAAY;;;;;;;;;;;;kDAGhB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAA0C;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,WAAU;gDACV,OAAO,YAAY,KAAK;gDACxB,UAAU,CAAC,IAAM,eAAe;wDAAE,GAAG,WAAW;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI9E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,mBAAmB;wCAClC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;YASR,oCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAK,UAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAS,WAAU;0DAA0C;;;;;;0DAG5E,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,MAAM;gDAC5B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGnF,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAyB;;kEAErG,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAG3B,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA0C;;;;;;0DAG9E,8OAAC;gDACC,IAAG;gDACH,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;kEAEpF,8OAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WACE,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK,eAAe,IAAI,EAC9C,GAAG,CAAC,CAAC,yBACJ,8OAAC;4DAAyB,OAAO,SAAS,EAAE;sEACzC,SAAS,IAAI;2DADH,SAAS,EAAE;;;;;;;;;;;;;;;;;kDAMhC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA0C;;;;;;0DAG1E,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,IAAI;gDAC1B,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;kDAGjF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,SAAQ;gDAAc,WAAU;0DAA0C;;;;;;0DAGjF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,QAAQ;gDACR,WAAU;gDACV,OAAO,eAAe,WAAW;gDACjC,UAAU,CAAC,IAAM,kBAAkB;wDAAE,GAAG,cAAc;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;;;;;;;;;;;;;;;;;;0CAI1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAS,WAAU;sDAA+C;;;;;;sDAGjF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,WAAU;;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAa,WAAU;sDAA+C;;;;;;sDAGrF,8OAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,8OAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;;;;;;;8CAG5B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAiB,WAAU;sDAA+C;;;;;;sDAGzF,8OAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;gDACnB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;;8CAM9B,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,OAAO,UAAU,KAAK;oDACtB,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACpE,WAAU;;;;;;8DAEZ,8OAAC;oDACC,MAAK;oDACL,OAAO,UAAU,GAAG;oDACpB,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,KAAK,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAClE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAG5D,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCACZ,0BAA0B,MAAM;oCAAC;;;;;;;;;;;;;oBAGrC,0BAA0B,MAAM,KAAK,kBACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,8OAAC;gCAAG,WAAU;0CACX,aAAa,MAAM,KAAK,IAAI,oBAAoB;;;;;;0CAEnD,8OAAC;gCAAE,WAAU;0CACV,aAAa,MAAM,KAAK,IACrB,kDACA;;;;;;;;;;;6CAIR,8OAAC;wBAAG,WAAU;kCACX,0BAA0B,GAAG,CAAC,CAAC,4BAC9B,8OAAC;gCAAwB,WAAU;0CACjC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAW,CAAC,kCAAkC,CAAC;oDAC/C,OAAO;wDAAE,iBAAiB,AAAC,YAAoB,iBAAiB,EAAE,SAAS;oDAAU;;;;;;8DAC1F,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEACV,YAAY,WAAW;;;;;;sEAE1B,8OAAC;4DAAE,WAAU;;gEACV,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;gEAAG;gEAAK,YAAoB,iBAAiB,EAAE,QAAQ;;;;;;;;;;;;;;;;;;;sDAI3G,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,oBAAoB,EACpC,YAAY,IAAI,KAAK,WAAW,mBAAmB,gBACnD;;oDACC,YAAY,IAAI,KAAK,WAAW,MAAM;oDAAI;oDAAE,YAAY,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;+BAlBvE,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;AA6BrC", "debugId": null}}]}