{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;AAEO,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/chat/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { SendIcon, MessageCircleIcon, PlusIcon, BotIcon, UserIcon, TrashIcon, EditIcon, SparklesIcon, TrendingUpIcon, CheckCircleIcon, ShoppingCartIcon, DollarSignIcon } from 'lucide-react'\nimport { Database } from '@/lib/types/database'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { Input } from '@/components/ui/Input'\nimport { Loading } from '@/components/ui/Loading'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport toast from 'react-hot-toast'\n\ntype ChatConversation = Database['public']['Tables']['chat_conversations']['Row']\ntype ChatMessage = Database['public']['Tables']['chat_messages']['Row']\n\nexport default function ChatPage() {\n  const [conversations, setConversations] = useState<ChatConversation[]>([])\n  const [currentConversation, setCurrentConversation] = useState<string | null>(null)\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n  const [newMessage, setNewMessage] = useState('')\n  const [loading, setLoading] = useState(true)\n  const [sending, setSending] = useState(false)\n  const [editingConversation, setEditingConversation] = useState<string | null>(null)\n  const [editingTitle, setEditingTitle] = useState('')\n  const [showSuggestions, setShowSuggestions] = useState(true)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const supabase = createClient()\n\n  const quickSuggestions = [\n    { icon: CheckCircleIcon, text: \"What tasks should I prioritize today?\", color: \"text-blue-600\" },\n    { icon: DollarSignIcon, text: \"How is my budget looking this month?\", color: \"text-green-600\" },\n    { icon: ShoppingCartIcon, text: \"Help me create a grocery list\", color: \"text-purple-600\" },\n    { icon: TrendingUpIcon, text: \"Give me insights on my spending\", color: \"text-orange-600\" },\n  ]\n\n  useEffect(() => {\n    fetchConversations()\n\n    // Set up real-time subscriptions\n    const messagesSubscription = supabase\n      .channel('chat_messages_changes')\n      .on('postgres_changes',\n        { event: '*', schema: 'public', table: 'chat_messages' },\n        (payload) => {\n          console.log('Chat message change received:', payload)\n          if (currentConversation && payload.new && (payload.new as any).conversation_id === currentConversation) {\n            fetchMessages(currentConversation)\n          }\n        }\n      )\n      .subscribe()\n\n    return () => {\n      supabase.removeChannel(messagesSubscription)\n    }\n  }, [])\n\n  useEffect(() => {\n    if (currentConversation) {\n      fetchMessages(currentConversation)\n      setShowSuggestions(false)\n    }\n  }, [currentConversation])\n\n  useEffect(() => {\n    scrollToBottom()\n  }, [messages])\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  const fetchConversations = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .select('*')\n        .order('updated_at', { ascending: false })\n\n      if (error) throw error\n      setConversations(data || [])\n      \n      if (data && data.length > 0 && !currentConversation) {\n        setCurrentConversation(data[0].id)\n      }\n    } catch (error) {\n      console.error('Error fetching conversations:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchMessages = async (conversationId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('chat_messages')\n        .select('*')\n        .eq('conversation_id', conversationId)\n        .order('created_at', { ascending: true })\n\n      if (error) throw error\n      setMessages(data || [])\n    } catch (error) {\n      console.error('Error fetching messages:', error)\n    }\n  }\n\n  const createNewConversation = async () => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return\n\n      const { data, error } = await supabase\n        .from('chat_conversations')\n        .insert([\n          {\n            user_id: user.id,\n            title: 'New Conversation',\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      setConversations([data, ...conversations])\n      setCurrentConversation(data.id)\n      setMessages([])\n      setShowSuggestions(true)\n      toast.success('New conversation created!')\n    } catch (error) {\n      console.error('Error creating conversation:', error)\n      toast.error('Failed to create conversation. Please try again.')\n    }\n  }\n\n  const deleteConversation = async (conversationId: string) => {\n    if (!confirm('Are you sure you want to delete this conversation?')) return\n\n    try {\n      const { error } = await supabase\n        .from('chat_conversations')\n        .delete()\n        .eq('id', conversationId)\n\n      if (error) throw error\n\n      setConversations(conversations.filter(c => c.id !== conversationId))\n      if (currentConversation === conversationId) {\n        setCurrentConversation(null)\n        setMessages([])\n        setShowSuggestions(true)\n      }\n      toast.success('Conversation deleted successfully!')\n    } catch (error) {\n      console.error('Error deleting conversation:', error)\n      toast.error('Failed to delete conversation. Please try again.')\n    }\n  }\n\n  const updateConversationTitle = async (conversationId: string, newTitle: string) => {\n    try {\n      const { error } = await supabase\n        .from('chat_conversations')\n        .update({ title: newTitle })\n        .eq('id', conversationId)\n\n      if (error) throw error\n\n      setConversations(conversations.map(c =>\n        c.id === conversationId ? { ...c, title: newTitle } : c\n      ))\n      setEditingConversation(null)\n      setEditingTitle('')\n      toast.success('Conversation title updated!')\n    } catch (error) {\n      console.error('Error updating conversation title:', error)\n      toast.error('Failed to update title. Please try again.')\n    }\n  }\n\n  const handleSuggestionClick = (suggestion: string) => {\n    setNewMessage(suggestion)\n    setShowSuggestions(false)\n  }\n\n  const sendMessage = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newMessage.trim() || !currentConversation || sending) return\n\n    setSending(true)\n    const messageText = newMessage.trim()\n    setNewMessage('')\n    setShowSuggestions(false)\n\n    try {\n      // Add user message to UI immediately\n      const userMessage: ChatMessage = {\n        id: 'temp-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'user',\n        content: messageText,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, userMessage])\n\n      // Add typing indicator\n      const typingMessage: ChatMessage = {\n        id: 'typing-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'assistant',\n        content: '...',\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, typingMessage])\n\n      // Send to API\n      const response = await fetch('/api/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: messageText,\n          conversationId: currentConversation,\n        }),\n      })\n\n      if (!response.ok) {\n        const errorData = await response.json()\n        throw new Error(errorData.error || 'Failed to send message')\n      }\n\n      const { response: aiResponse } = await response.json()\n\n      // Remove typing indicator and add AI response\n      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))\n\n      const aiMessage: ChatMessage = {\n        id: 'temp-ai-' + Date.now(),\n        conversation_id: currentConversation,\n        role: 'assistant',\n        content: aiResponse,\n        created_at: new Date().toISOString(),\n      }\n      setMessages(prev => [...prev, aiMessage])\n\n      // Update conversation title if it's the first message\n      if (messages.length === 0) {\n        const title = messageText.length > 30 ? messageText.substring(0, 30) + '...' : messageText\n        updateConversationTitle(currentConversation, title)\n      }\n\n      // Refresh messages from database to get real IDs\n      setTimeout(() => fetchMessages(currentConversation), 500)\n    } catch (error) {\n      console.error('Error sending message:', error)\n      // Remove typing indicator on error\n      setMessages(prev => prev.filter(msg => !msg.id.startsWith('typing-')))\n      toast.error('Failed to send message. Please try again.')\n    } finally {\n      setSending(false)\n    }\n  }\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading AI Chat...\" className=\"h-64\" />\n  }\n\n  return (\n    <div className=\"h-[calc(100vh-8rem)] flex\">\n      {/* Conversations Sidebar */}\n      <div className=\"w-1/4 bg-white border-r border-gray-200 flex flex-col\">\n        <div className=\"p-4 border-b border-gray-200\">\n          <button\n            onClick={createNewConversation}\n            className=\"w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <PlusIcon className=\"h-4 w-4 mr-2\" />\n            New Chat\n          </button>\n        </div>\n        <div className=\"flex-1 overflow-y-auto\">\n          {conversations.length === 0 ? (\n            <div className=\"p-4 text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-8 w-8 mb-2\" />\n              <p className=\"text-sm\">No conversations yet</p>\n            </div>\n          ) : (\n            <div className=\"space-y-1 p-2\">\n              {conversations.map((conversation) => (\n                <button\n                  key={conversation.id}\n                  onClick={() => setCurrentConversation(conversation.id)}\n                  className={`w-full text-left p-3 rounded-lg text-sm ${\n                    currentConversation === conversation.id\n                      ? 'bg-blue-100 text-blue-900'\n                      : 'hover:bg-gray-100'\n                  }`}\n                >\n                  <div className=\"font-medium truncate\">{conversation.title}</div>\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    {new Date(conversation.updated_at).toLocaleDateString()}\n                  </div>\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Chat Area */}\n      <div className=\"flex-1 flex flex-col\">\n        {currentConversation ? (\n          <>\n            {/* Messages */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n              {messages.length === 0 ? (\n                <div className=\"text-center text-gray-500 mt-8\">\n                  <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-4\" />\n                  <h3 className=\"text-lg font-medium\">Start a conversation</h3>\n                  <p className=\"text-sm\">Ask me anything about managing your life!</p>\n                </div>\n              ) : (\n                messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${\n                      message.role === 'user' ? 'justify-end' : 'justify-start'\n                    }`}\n                  >\n                    <div\n                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                        message.role === 'user'\n                          ? 'bg-blue-600 text-white'\n                          : 'bg-gray-200 text-gray-900'\n                      }`}\n                    >\n                      <p className=\"text-sm\">{message.content}</p>\n                      <p className={`text-xs mt-1 ${\n                        message.role === 'user' ? 'text-blue-200' : 'text-gray-500'\n                      }`}>\n                        {new Date(message.created_at).toLocaleTimeString()}\n                      </p>\n                    </div>\n                  </div>\n                ))\n              )}\n              <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input */}\n            <div className=\"border-t border-gray-200 p-4\">\n              <form onSubmit={sendMessage} className=\"flex space-x-2\">\n                <input\n                  type=\"text\"\n                  value={newMessage}\n                  onChange={(e) => setNewMessage(e.target.value)}\n                  placeholder=\"Type your message...\"\n                  className=\"flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  disabled={sending}\n                />\n                <button\n                  type=\"submit\"\n                  disabled={!newMessage.trim() || sending}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <SendIcon className=\"h-4 w-4\" />\n                </button>\n              </form>\n            </div>\n          </>\n        ) : (\n          <div className=\"flex-1 flex items-center justify-center\">\n            <div className=\"text-center text-gray-500\">\n              <MessageCircleIcon className=\"mx-auto h-12 w-12 mb-4\" />\n              <h3 className=\"text-lg font-medium\">Select a conversation</h3>\n              <p className=\"text-sm\">Choose a conversation from the sidebar or start a new one</p>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA;AAEA;AAZA;;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,mBAAmB;QACvB;YAAE,MAAM,+NAAA,CAAA,kBAAe;YAAE,MAAM;YAAyC,OAAO;QAAgB;QAC/F;YAAE,MAAM,sNAAA,CAAA,iBAAc;YAAE,MAAM;YAAwC,OAAO;QAAiB;QAC9F;YAAE,MAAM,0NAAA,CAAA,mBAAgB;YAAE,MAAM;YAAiC,OAAO;QAAkB;QAC1F;YAAE,MAAM,sNAAA,CAAA,iBAAc;YAAE,MAAM;YAAmC,OAAO;QAAkB;KAC3F;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,iCAAiC;QACjC,MAAM,uBAAuB,SAC1B,OAAO,CAAC,yBACR,EAAE,CAAC,oBACF;YAAE,OAAO;YAAK,QAAQ;YAAU,OAAO;QAAgB,GACvD,CAAC;YACC,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,IAAI,uBAAuB,QAAQ,GAAG,IAAI,AAAC,QAAQ,GAAG,CAAS,eAAe,KAAK,qBAAqB;gBACtG,cAAc;YAChB;QACF,GAED,SAAS;QAEZ,OAAO;YACL,SAAS,aAAa,CAAC;QACzB;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,qBAAqB;YACvB,cAAc;YACd,mBAAmB;QACrB;IACF,GAAG;QAAC;KAAoB;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;YAE3B,IAAI,QAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,qBAAqB;gBACnD,uBAAuB,IAAI,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB,gBACtB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YAEzC,IAAI,OAAO,MAAM;YACjB,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YACtD,IAAI,CAAC,MAAM;YAEX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC;gBACN;oBACE,SAAS,KAAK,EAAE;oBAChB,OAAO;gBACT;aACD,EACA,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,iBAAiB;gBAAC;mBAAS;aAAc;YACzC,uBAAuB,KAAK,EAAE;YAC9B,YAAY,EAAE;YACd,mBAAmB;YACnB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,uDAAuD;QAEpE,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,sBACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpD,IAAI,wBAAwB,gBAAgB;gBAC1C,uBAAuB;gBACvB,YAAY,EAAE;gBACd,mBAAmB;YACrB;YACA,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,0BAA0B,OAAO,gBAAwB;QAC7D,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,sBACL,MAAM,CAAC;gBAAE,OAAO;YAAS,GACzB,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,iBAAiB,cAAc,GAAG,CAAC,CAAA,IACjC,EAAE,EAAE,KAAK,iBAAiB;oBAAE,GAAG,CAAC;oBAAE,OAAO;gBAAS,IAAI;YAExD,uBAAuB;YACvB,gBAAgB;YAChB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc;QACd,mBAAmB;IACrB;IAEA,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,uBAAuB,SAAS;QAE3D,WAAW;QACX,MAAM,cAAc,WAAW,IAAI;QACnC,cAAc;QACd,mBAAmB;QAEnB,IAAI;YACF,qCAAqC;YACrC,MAAM,cAA2B;gBAC/B,IAAI,UAAU,KAAK,GAAG;gBACtB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAE1C,uBAAuB;YACvB,MAAM,gBAA6B;gBACjC,IAAI,YAAY,KAAK,GAAG;gBACxB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAc;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,aAAa;gBACxC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,EAAE,UAAU,UAAU,EAAE,GAAG,MAAM,SAAS,IAAI;YAEpD,8CAA8C;YAC9C,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC;YAE1D,MAAM,YAAyB;gBAC7B,IAAI,aAAa,KAAK,GAAG;gBACzB,iBAAiB;gBACjB,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YAExC,sDAAsD;YACtD,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,QAAQ,YAAY,MAAM,GAAG,KAAK,YAAY,SAAS,CAAC,GAAG,MAAM,QAAQ;gBAC/E,wBAAwB,qBAAqB;YAC/C;YAEA,iDAAiD;YACjD,WAAW,IAAM,cAAc,sBAAsB;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,mCAAmC;YACnC,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC;YAC1D,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAAqB,WAAU;;;;;;IAChE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIzC,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,KAAK,kBACxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4NAAA,CAAA,oBAAiB;oCAAC,WAAU;;;;;;8CAC7B,8OAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;iDAGzB,8OAAC;4BAAI,WAAU;sCACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;oCAEC,SAAS,IAAM,uBAAuB,aAAa,EAAE;oCACrD,WAAW,CAAC,wCAAwC,EAClD,wBAAwB,aAAa,EAAE,GACnC,8BACA,qBACJ;;sDAEF,8OAAC;4CAAI,WAAU;sDAAwB,aAAa,KAAK;;;;;;sDACzD,8OAAC;4CAAI,WAAU;sDACZ,IAAI,KAAK,aAAa,UAAU,EAAE,kBAAkB;;;;;;;mCAVlD,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;0BAoBhC,8OAAC;gBAAI,WAAU;0BACZ,oCACC;;sCAEE,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4NAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAG,WAAU;sDAAsB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;2CAGzB,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC;wCAEC,WAAW,CAAC,KAAK,EACf,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;kDAEF,cAAA,8OAAC;4CACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;;8DAEF,8OAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;8DACvC,8OAAC;oDAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;8DACC,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB;;;;;;;;;;;;uCAhB/C,QAAQ,EAAE;;;;;8CAsBrB,8OAAC;oCAAI,KAAK;;;;;;;;;;;;sCAIZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAa,WAAU;;kDACrC,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCACC,MAAK;wCACL,UAAU,CAAC,WAAW,IAAI,MAAM;wCAChC,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;iDAM5B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4NAAA,CAAA,oBAAiB;gCAAC,WAAU;;;;;;0CAC7B,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}]}